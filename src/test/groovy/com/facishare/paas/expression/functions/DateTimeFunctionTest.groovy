package com.facishare.paas.expression.functions

import com.facishare.paas.expression.ExpressionEngine
import com.facishare.paas.expression.type.*
import spock.lang.Specification

/**
 * DateTimeFunction 单元测试
 * 测试 NOW, DATETIMEVALUE, LONG<PERSON>TETIMEVALUE, YEARS, MONTHS, DAYS, HOURS, MINUTES, DATEVALUE, LONGDATEVALUE, DATE, TODAY, YEAR, MONTH, DAY, DATETODATETIME, DATETIMETODATE, DATETIMETOTIME, TOTIMESTAMP 等方法的 compile 和 evaluate 场景
 */
class DateTimeFunctionTest extends Specification {

    ExpressionEngine engine

    def setup() {
        engine = new ExpressionEngine()
    }

    def "test datetime functions compile"() {
        when:
        engine.compile(expression)
        then:
        noExceptionThrown()
        where:
        expression << [
                'NOW()',
                'DATETIMEVALUE("2023-01-01 12:00:00")',
                'LONGDATETIMEVALUE(1672531200000L)',
                'YEARS(5)',
                'MONTHS(3)',
                'DAYS(10)',
                'HOURS(2)',
                'MINUTES(30)',
                'DATEVALUE("2023-01-01")',
                'LONGDATEVALUE(1672531200000L)',
                'DATE(2023, 1, 1)',
                'DATE()',
                'TODAY()',
                'YEAR(DATEVALUE("2023-01-01"))',
                'MONTH(DATEVALUE("2023-01-01"))',
                'DAY(DATEVALUE("2023-01-01"))',
                'DATETODATETIME(DATEVALUE("2023-01-01"))',
                'DATETIMETODATE(DATETIMEVALUE("2023-01-01 12:00:00"))',
                'DATETIMETOTIME(DATETIMEVALUE("2023-01-01 12:00:00"))',
                'TOTIMESTAMP(DATEVALUE("2023-01-01"))',
                'TOTIMESTAMP(DATETIMEVALUE("2023-01-01 12:00:00"))'
        ]
    }

    def "test NOW evaluate"() {
        when:
        def result = engine.evaluate('NOW()', [:])
        then:
        result instanceof PDateTime
        // NOW() should return current time, so we just check it's recent
        Math.abs(result.toTimeStamp() - System.currentTimeMillis()) < 5000
    }

    def "test DATETIMEVALUE evaluate"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression                                  | bindings                                      || expectedResult
        'DATETIMEVALUE("2023-01-01 12:00:00")'      | [:]                                           || PDateTime.of("2023-01-01 12:00:00")
        'DATETIMEVALUE("2023-12-31 23:59:59")'      | [:]                                           || PDateTime.of("2023-12-31 23:59:59")
        'DATETIMEVALUE(dateStr)'                    | [dateStr: "2023-06-15 10:30:45"]             || PDateTime.of("2023-06-15 10:30:45")
    }

    def "test LONGDATETIMEVALUE evaluate"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result instanceof PDateTime
        result.toTimeStamp() > 0
        where:
        expression                          | bindings
        'LONGDATETIMEVALUE(1672531200000L)' | [:]
        'LONGDATETIMEVALUE(timestamp)'      | [timestamp: 1672531200000L]
    }

    def "test time unit constructors evaluate"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result.getValue() == expectedValue
        where:
        expression          | bindings              || expectedValue
        'YEARS(5)'          | [:]                   || 5
        'YEARS(value)'      | [value: 3]            || 3
        'MONTHS(12)'        | [:]                   || 12
        'MONTHS(value)'     | [value: 6]            || 6
        'DAYS(30)'          | [:]                   || 30
        'DAYS(value)'       | [value: 15]           || 15
        'HOURS(24)'         | [:]                   || 24
        'HOURS(value)'      | [value: 12]           || 12
        'MINUTES(60)'       | [:]                   || 60
        'MINUTES(value)'    | [value: 30]           || 30
    }

    def "test DATEVALUE evaluate"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression                      | bindings                          || expectedResult
        'DATEVALUE("2023-01-01")'       | [:]                               || PDate.of("2023-01-01")
        'DATEVALUE("2023-12-31")'       | [:]                               || PDate.of("2023-12-31")
        'DATEVALUE(dateStr)'            | [dateStr: "2023-06-15"]           || PDate.of("2023-06-15")
    }

    def "test LONGDATEVALUE evaluate"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result instanceof PDate
        result.toTimeStamp() > 0
        where:
        expression                      | bindings
        'LONGDATEVALUE(1672531200000L)' | [:]
        'LONGDATEVALUE(timestamp)'      | [timestamp: 1672531200000L]
    }

    def "test DATE constructor evaluate"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression                      | bindings                                      || expectedResult
        'DATE(2023, 1, 1)'              | [:]                                           || PDate.of(2023, 1, 1)
        'DATE(2023, 12, 31)'            | [:]                                           || PDate.of(2023, 12, 31)
        'DATE(year, month, day)'        | [year: 2023, month: 6, day: 15]               || PDate.of(2023, 6, 15)
    }

    def "test DATE and TODAY evaluate"() {
        when:
        def dateResult = engine.evaluate('DATE()', [:])
        def todayResult = engine.evaluate('TODAY()', [:])
        then:
        dateResult instanceof PDate
        todayResult instanceof PDate
        // Both should return today's date
        dateResult.getYear() == todayResult.getYear()
        dateResult.getMonth() == todayResult.getMonth()
        dateResult.getDay() == todayResult.getDay()
    }

    def "test YEAR, MONTH, DAY evaluate"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression                                      | bindings                                      || expectedResult
        'YEAR(DATEVALUE("2023-06-15"))'                 | [:]                                           || 2023
        'MONTH(DATEVALUE("2023-06-15"))'                | [:]                                           || 6
        'DAY(DATEVALUE("2023-06-15"))'                  | [:]                                           || 15
        'YEAR(DATETIMEVALUE("2023-06-15 10:30:45"))'    | [:]                                           || 2023
        'MONTH(DATETIMEVALUE("2023-06-15 10:30:45"))'   | [:]                                           || 6
        'DAY(DATETIMEVALUE("2023-06-15 10:30:45"))'     | [:]                                           || 15
        'YEAR(date)'                                    | [date: PDate.of("2023-12-31")]               || 2023
        'MONTH(date)'                                   | [date: PDate.of("2023-12-31")]               || 12
        'DAY(date)'                                     | [date: PDate.of("2023-12-31")]               || 31
    }

    def "test date conversion functions evaluate"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression                                              | bindings                                      || expectedResult
        'DATETODATETIME(DATEVALUE("2023-06-15"))'               | [:]                                           || PDateTime.of(2023, 6, 15, 0, 0, 0)
        'DATETIMETODATE(DATETIMEVALUE("2023-06-15 10:30:45"))'  | [:]                                           || PDate.of(2023, 6, 15)
        'DATETIMETOTIME(DATETIMEVALUE("2023-06-15 10:30:45"))'  | [:]                                           || PTime.of(PDateTime.of("2023-06-15 10:30:45").toTimeStamp())
    }

    def "test TOTIMESTAMP evaluate"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result instanceof Long
        result > 0
        where:
        expression                                      | bindings
        'TOTIMESTAMP(DATEVALUE("2023-01-01"))'          | [:]
        'TOTIMESTAMP(DATETIMEVALUE("2023-01-01 12:00:00"))' | [:]
        'TOTIMESTAMP(DATETIMETOTIME(DATETIMEVALUE("2023-01-01 12:00:00")))' | [:]
    }

    def "test complex datetime expressions"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression                                                      | bindings                                      || expectedResult
        'YEAR(DATETIMETODATE(DATETIMEVALUE("2023-06-15 10:30:45")))'    | [:]                                           || 2023
        'MONTH(DATETODATETIME(DATEVALUE("2023-06-15")))'                | [:]                                           || 6
        'DAY(DATEVALUE("2023-06-15"))'                                  | [:]                                           || 15
        'YEAR(DATETIMETODATE(DATETIMEVALUE("2023-12-31 23:59:59")))'    | [:]                                           || 2023
    }

    def "test datetime edge cases"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression                                              | bindings                                      || expectedResult
        'YEAR(DATEVALUE("2023-12-31"))'                        | [:]                                           || 2023
        'MONTH(DATEVALUE("2023-01-01"))'                       | [:]                                           || 1
        'DAY(DATEVALUE("2023-12-31"))'                         | [:]                                           || 31
        'YEAR(DATETIMEVALUE("2023-06-15 23:59:59"))'           | [:]                                           || 2023
        'MONTH(DATETIMEVALUE("2023-06-15 00:00:00"))'          | [:]                                           || 6
        'DAY(DATETIMEVALUE("2023-06-15 12:30:45"))'            | [:]                                           || 15
    }

    def "test leap year and boundary dates"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression                                              | bindings                                      || expectedResult
        // Leap year tests
        'YEAR(DATEVALUE("2024-02-29"))'                        | [:]                                           || 2024
        'MONTH(DATEVALUE("2024-02-29"))'                       | [:]                                           || 2
        'DAY(DATEVALUE("2024-02-29"))'                         | [:]                                           || 29
        'YEAR(DATEVALUE("2023-02-28"))'                        | [:]                                           || 2023
        'MONTH(DATEVALUE("2023-02-28"))'                       | [:]                                           || 2
        'DAY(DATEVALUE("2023-02-28"))'                         | [:]                                           || 28
        // Year boundaries
        'YEAR(DATEVALUE("1900-01-01"))'                        | [:]                                           || 1900
        'YEAR(DATEVALUE("2099-12-31"))'                        | [:]                                           || 2099
        'YEAR(DATEVALUE("2000-01-01"))'                        | [:]                                           || 2000
        // Month boundaries
        'MONTH(DATEVALUE("2023-01-31"))'                       | [:]                                           || 1
        'MONTH(DATEVALUE("2023-12-01"))'                       | [:]                                           || 12
        // Day boundaries
        'DAY(DATEVALUE("2023-01-01"))'                         | [:]                                           || 1
        'DAY(DATEVALUE("2023-01-31"))'                         | [:]                                           || 31
        'DAY(DATEVALUE("2023-04-30"))'                         | [:]                                           || 30
        'DAY(DATEVALUE("2023-02-28"))'                         | [:]                                           || 28
    }

    def "test datetime with different time formats"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression                                              | bindings                                      || expectedResult
        // Different time formats
        'YEAR(DATETIMEVALUE("2023-01-01 00:00:00"))'           | [:]                                           || 2023
        'YEAR(DATETIMEVALUE("2023-01-01 23:59:59"))'           | [:]                                           || 2023
        'MONTH(DATETIMEVALUE("2023-12-01 12:00:00"))'          | [:]                                           || 12
        'DAY(DATETIMEVALUE("2023-01-31 06:30:45"))'            | [:]                                           || 31
        // Midnight and noon
        'YEAR(DATETIMEVALUE("2023-06-15 00:00:00"))'           | [:]                                           || 2023
        'MONTH(DATETIMEVALUE("2023-06-15 12:00:00"))'          | [:]                                           || 6
        'DAY(DATETIMEVALUE("2023-06-15 23:59:59"))'            | [:]                                           || 15
        // Single digit time components
        'YEAR(DATETIMEVALUE("2023-01-01 01:01:01"))'           | [:]                                           || 2023
        'MONTH(DATETIMEVALUE("2023-09-09 09:09:09"))'          | [:]                                           || 9
        'DAY(DATETIMEVALUE("2023-01-09 09:09:09"))'            | [:]                                           || 9
    }

    def "test BigDecimal parameters for date functions"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression                                              | bindings                                      || expectedResult
        // BigDecimal parameters for YEARS, MONTHS, DAYS, HOURS, MINUTES
        'YEARS(yearValue).getValue()'                          | [yearValue: new BigDecimal("5")]              || 5
        'MONTHS(monthValue).getValue()'                        | [monthValue: new BigDecimal("12")]            || 12
        'DAYS(dayValue).getValue()'                            | [dayValue: new BigDecimal("30")]              || 30
        'HOURS(hourValue).getValue()'                          | [hourValue: new BigDecimal("24")]             || 24
        'MINUTES(minuteValue).getValue()'                      | [minuteValue: new BigDecimal("60")]           || 60
        // BigDecimal parameters for DATE constructor
        'YEAR(DATE(yearVal, monthVal, dayVal))'                | [yearVal: new BigDecimal("2023"), monthVal: new BigDecimal("6"), dayVal: new BigDecimal("15")] || 2023
        'MONTH(DATE(yearVal, monthVal, dayVal))'               | [yearVal: new BigDecimal("2023"), monthVal: new BigDecimal("6"), dayVal: new BigDecimal("15")] || 6
        'DAY(DATE(yearVal, monthVal, dayVal))'                 | [yearVal: new BigDecimal("2023"), monthVal: new BigDecimal("6"), dayVal: new BigDecimal("15")] || 15
    }

    def "test timestamp conversion edge cases"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result instanceof Long
        result > 0
        where:
        expression                                              | bindings
        // Unix epoch
        'TOTIMESTAMP(LONGDATEVALUE(0L))'                       | [:]
        'TOTIMESTAMP(LONGDATETIMEVALUE(0L))'                   | [:]
        // Large timestamps
        'TOTIMESTAMP(LONGDATEVALUE(2147483647000L))'           | [:]
        'TOTIMESTAMP(LONGDATETIMEVALUE(2147483647000L))'       | [:]
        // Recent timestamps
        'TOTIMESTAMP(LONGDATEVALUE(1672531200000L))'           | [:]
        'TOTIMESTAMP(LONGDATETIMEVALUE(1672531200000L))'       | [:]
    }

    def "test date arithmetic and comparisons"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression                                              | bindings                                      || expectedResult
        // Date comparisons through year/month/day
        'YEAR(DATEVALUE("2023-06-15")) == YEAR(DATEVALUE("2023-12-31"))' | [:]                               || true
        'MONTH(DATEVALUE("2023-06-15")) == MONTH(DATEVALUE("2024-06-15"))' | [:]                             || true
        'DAY(DATEVALUE("2023-06-15")) == DAY(DATEVALUE("2024-07-15"))'   | [:]                               || true
        'YEAR(DATEVALUE("2023-06-15")) != YEAR(DATEVALUE("2024-06-15"))' | [:]                               || true
        'MONTH(DATEVALUE("2023-06-15")) != MONTH(DATEVALUE("2023-07-15"))' | [:]                             || true
        'DAY(DATEVALUE("2023-06-15")) != DAY(DATEVALUE("2023-06-16"))'   | [:]                               || true
        // DateTime comparisons
        'YEAR(DATETIMEVALUE("2023-06-15 10:30:45")) == YEAR(DATETIMEVALUE("2023-12-31 23:59:59"))' | [:] || true
        'MONTH(DATETIMEVALUE("2023-06-15 10:30:45")) == MONTH(DATETIMEVALUE("2024-06-20 00:00:00"))' | [:] || true
        'DAY(DATETIMEVALUE("2023-06-15 10:30:45")) == DAY(DATETIMEVALUE("2024-07-15 12:00:00"))'   | [:] || true
    }

    def "test date conversion chain operations"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression                                                              | bindings                                      || expectedResult
        // Date -> DateTime -> Date conversion chain
        'YEAR(DATETIMETODATE(DATETODATETIME(DATEVALUE("2023-06-15"))))'         | [:]                                           || 2023
        'MONTH(DATETIMETODATE(DATETODATETIME(DATEVALUE("2023-06-15"))))'        | [:]                                           || 6
        'DAY(DATETIMETODATE(DATETODATETIME(DATEVALUE("2023-06-15"))))'          | [:]                                           || 15
        // DateTime -> Date -> DateTime conversion chain
        'YEAR(DATETODATETIME(DATETIMETODATE(DATETIMEVALUE("2023-06-15 10:30:45"))))' | [:]                                       || 2023
        'MONTH(DATETODATETIME(DATETIMETODATE(DATETIMEVALUE("2023-06-15 10:30:45"))))' | [:]                                      || 6
        'DAY(DATETODATETIME(DATETIMETODATE(DATETIMEVALUE("2023-06-15 10:30:45"))))'  | [:]                                       || 15
        // Timestamp conversion chains
        'YEAR(LONGDATEVALUE(TOTIMESTAMP(DATEVALUE("2023-06-15"))))'             | [:]                                           || 2023
        'MONTH(LONGDATEVALUE(TOTIMESTAMP(DATEVALUE("2023-06-15"))))'            | [:]                                           || 6
        'DAY(LONGDATEVALUE(TOTIMESTAMP(DATEVALUE("2023-06-15"))))'              | [:]                                           || 15
    }

    def "test time unit edge cases"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result.getValue() == expectedValue
        where:
        expression                      | bindings                              || expectedValue
        // Zero values
        'YEARS(0)'                      | [:]                                   || 0
        'MONTHS(0)'                     | [:]                                   || 0
        'DAYS(0)'                       | [:]                                   || 0
        'HOURS(0)'                      | [:]                                   || 0
        'MINUTES(0)'                    | [:]                                   || 0
        // Large values
        'YEARS(999)'                    | [:]                                   || 999
        'MONTHS(999)'                   | [:]                                   || 999
        'DAYS(999)'                     | [:]                                   || 999
        'HOURS(999)'                    | [:]                                   || 999
        'MINUTES(999)'                  | [:]                                   || 999
        // Negative values (if supported)
        'YEARS(-1)'                     | [:]                                   || -1
        'MONTHS(-1)'                    | [:]                                   || -1
        'DAYS(-1)'                      | [:]                                   || -1
        'HOURS(-1)'                     | [:]                                   || -1
        'MINUTES(-1)'                   | [:]                                   || -1
    }

    def "test complex nested datetime operations"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression                                                                      | bindings                                      || expectedResult
        // Complex nested operations
        'YEAR(DATETIMETODATE(DATETIMEVALUE("2023-06-15 10:30:45")))'                   | [:]                                           || 2023
        'MONTH(DATETODATETIME(DATEVALUE("2023-06-15")))'                               | [:]                                           || 6
        'DAY(LONGDATEVALUE(TOTIMESTAMP(DATEVALUE("2023-06-15"))))'                     | [:]                                           || 15
        // Multiple function calls in one expression
        'YEAR(date1) == YEAR(date2)'                                                   | [date1: PDate.of("2023-01-01"), date2: PDate.of("2023-12-31")] || true
        'MONTH(date1) != MONTH(date2)'                                                 | [date1: PDate.of("2023-01-15"), date2: PDate.of("2023-02-15")] || true
        'DAY(date1) == DAY(date2)'                                                     | [date1: PDate.of("2023-01-15"), date2: PDate.of("2023-02-15")] || true
        // Arithmetic with date components
        'YEAR(DATEVALUE("2023-06-15")) + 1'                                           | [:]                                           || 2024
        'MONTH(DATEVALUE("2023-06-15")) * 2'                                          | [:]                                           || 12
        'DAY(DATEVALUE("2023-06-15")) - 5'                                            | [:]                                           || 10
    }

    def "test multiple date operations"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression                                                                      | bindings                                      || expectedResult
        // Simplified multiple date operations to reduce memory usage
        'YEAR(DATEVALUE("2023-01-01")) + YEAR(DATEVALUE("2023-02-01"))'                | [:]                                           || 4046
        'MONTH(DATEVALUE("2023-01-15")) + MONTH(DATEVALUE("2023-02-15"))'              | [:]                                           || 3
        'DAY(DATEVALUE("2023-01-10")) + DAY(DATEVALUE("2023-02-20"))'                  | [:]                                           || 30
        // Simplified datetime operations
        'YEAR(DATETIMEVALUE("2023-01-01 10:00:00")) + MONTH(DATETIMEVALUE("2023-02-15 15:30:45"))' | [:] || 2025
    }
}
