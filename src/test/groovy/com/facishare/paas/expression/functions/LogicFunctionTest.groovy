package com.facishare.paas.expression.functions

import com.facishare.paas.expression.ExpressionEngine
import spock.lang.Specification

/**
 * LogicFunction 单元测试
 * 测试 NOT, AND, OR, IF, ISNULL, NOTNULL, ISBLANK, NOTBLANK, ISNUMBER, NULLVALUE, NULL2DEFAULT 等方法的 compile 和 evaluate 场景
 */
class LogicFunctionTest extends Specification {

    ExpressionEngine engine

    def setup() {
        engine = new ExpressionEngine()
    }

    def "test logic functions compile"() {
        when:
        engine.compile(expression)
        then:
        noExceptionThrown()
        where:
        expression << [
                'NOT(true)',
                'NOT(false)',
                'AND(true, true)',
                'AND(true, false, true)',
                'OR(false, false)',
                'OR(true, false, false)',
                'IF(true, "yes", "no")',
                'IF(false, 1, 2)',
                'ISNULL(null)',
                'ISNULL("test")',
                'NOTNULL("test")',
                'NOTNULL(null)',
                'ISBLANK("")',
                'ISBLANK("test")',
                'NOTBLANK("test")',
                'NOTBLANK("")',
                'ISNUMBER("123")',
                'ISNUMBER("abc")',
                'NULLVALUE(null, "default")',
                'NULLVALUE("value", "default")',
                'NULL2DEFAULT(null, "default")',
                'NULL2DEFAULT("value", "default")'
        ]
    }

    def "test NOT evaluate"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression          | bindings                  || expectedResult
        'NOT(true)'         | [:]                       || false
        'NOT(false)'        | [:]                       || true
        'NOT(value)'        | [value: true]             || false
        'NOT(value)'        | [value: false]            || true
    }

    def "test AND evaluate"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression                      | bindings                              || expectedResult
        'AND(true, true)'               | [:]                                   || true
        'AND(true, false)'              | [:]                                   || false
        'AND(false, true)'              | [:]                                   || false
        'AND(false, false)'             | [:]                                   || false
        'AND(true, true, true)'         | [:]                                   || true
        'AND(true, true, false)'        | [:]                                   || false
        'AND(a, b, c)'                  | [a: true, b: true, c: true]           || true
        'AND(a, b, c)'                  | [a: true, b: false, c: true]          || false
    }

    def "test OR evaluate"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression                      | bindings                              || expectedResult
        'OR(true, true)'                | [:]                                   || true
        'OR(true, false)'               | [:]                                   || true
        'OR(false, true)'               | [:]                                   || true
        'OR(false, false)'              | [:]                                   || false
        'OR(false, false, true)'        | [:]                                   || true
        'OR(false, false, false)'       | [:]                                   || false
        'OR(a, b, c)'                   | [a: false, b: false, c: true]         || true
        'OR(a, b, c)'                   | [a: false, b: false, c: false]        || false
    }

    def "test IF evaluate"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression                      | bindings                              || expectedResult
        'IF(true, "yes", "no")'         | [:]                                   || "yes"
        'IF(false, "yes", "no")'        | [:]                                   || "no"
        'IF(true, 1, 2)'                | [:]                                   || 1
        'IF(false, 1, 2)'               | [:]                                   || 2
        'IF(condition, value1, value2)' | [condition: true, value1: "a", value2: "b"]  || "a"
        'IF(condition, value1, value2)' | [condition: false, value1: "a", value2: "b"] || "b"
        'IF(x > 5, "big", "small")'     | [x: 10]                               || "big"
        'IF(x > 5, "big", "small")'     | [x: 3]                                || "small"
    }

    def "test ISNULL evaluate"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression          | bindings                              || expectedResult
        'ISNULL(null)'      | [:]                                   || true
        'ISNULL("test")'    | [:]                                   || false
        'ISNULL("")'        | [:]                                   || false
        'ISNULL([])'        | [:]                                   || true
        'ISNULL([1, 2])'    | [:]                                   || false
        'ISNULL(value)'     | [value: null]                         || true
        'ISNULL(value)'     | [value: "test"]                       || false
        'ISNULL(value)'     | [value: ""]                           || true
        'ISNULL(value)'     | [value: []]                           || true
        'ISNULL(value)'     | [value: [1, 2]]                       || false
    }

    def "test NOTNULL evaluate"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression          | bindings                              || expectedResult
        'NOTNULL(null)'     | [:]                                   || false
        'NOTNULL("test")'   | [:]                                   || true
        'NOTNULL("")'       | [:]                                   || false
        'NOTNULL([])'       | [:]                                   || false
        'NOTNULL([1, 2])'   | [:]                                   || true
        'NOTNULL(value)'    | [value: null]                         || false
        'NOTNULL(value)'    | [value: "test"]                       || true
        'NOTNULL(value)'    | [value: ""]                           || false
    }

    def "test ISBLANK and NOTBLANK evaluate"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression              | bindings                          || expectedResult
        'ISBLANK("")'           | [:]                               || true
        'ISBLANK("   ")'        | [:]                               || true
        'ISBLANK("test")'       | [:]                               || false
        'ISBLANK(null)'         | [:]                               || true
        'NOTBLANK("")'          | [:]                               || false
        'NOTBLANK("   ")'       | [:]                               || false
        'NOTBLANK("test")'      | [:]                               || true
        'NOTBLANK(null)'        | [:]                               || false
        'ISBLANK(value)'        | [value: ""]                       || true
        'NOTBLANK(value)'       | [value: "test"]                   || true
    }

    def "test ISNUMBER evaluate"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression              | bindings                          || expectedResult
        'ISNUMBER("123")'       | [:]                               || true
        'ISNUMBER("123.45")'    | [:]                               || true
        'ISNUMBER("-123")'      | [:]                               || true
        'ISNUMBER("+123")'      | [:]                               || true
        'ISNUMBER("123,456")'   | [:]                               || true
        'ISNUMBER("123.45E2")' | [:]                               || true
        'ISNUMBER("abc")'       | [:]                               || false
        'ISNUMBER("12a3")'      | [:]                               || true
        'ISNUMBER("")'          | [:]                               || false
        'ISNUMBER(value)'       | [value: "456"]                    || true
        'ISNUMBER(value)'       | [value: "abc"]                    || false
    }

    def "test NULLVALUE evaluate"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression                          | bindings                          || expectedResult
        'NULLVALUE(null, "default")'        | [:]                               || "default"
        'NULLVALUE("", "default")'          | [:]                               || "default"
        'NULLVALUE("value", "default")'     | [:]                               || null
        'NULLVALUE([], "default")'          | [:]                               || "default"
        'NULLVALUE([1, 2], "default")'      | [:]                               || null
        'NULLVALUE(cond, defaultVal)'       | [cond: null, defaultVal: "def"]   || "def"
        'NULLVALUE(cond, defaultVal)'       | [cond: "val", defaultVal: "def"]  || null
    }

    def "test NULL2DEFAULT evaluate"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression                              | bindings                              || expectedResult
        'NULL2DEFAULT(null, "default")'         | [:]                                   || "default"
        'NULL2DEFAULT("", "default")'           | [:]                                   || "default"
        'NULL2DEFAULT("value", "default")'      | [:]                                   || "value"
        'NULL2DEFAULT([], "default")'           | [:]                                   || "default"
        'NULL2DEFAULT([1, 2], "default")'       | [:]                                   || [1, 2]
        'NULL2DEFAULT(value, defaultVal)'       | [value: null, defaultVal: "def"]      || "def"
        'NULL2DEFAULT(value, defaultVal)'       | [value: "val", defaultVal: "def"]     || "val"
        'NULL2DEFAULT(value, defaultVal)'       | [value: "", defaultVal: "def"]        || "def"
    }

    def "test complex logic expressions"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression                                              | bindings                                      || expectedResult
        'AND(NOT(ISNULL(name)), NOTBLANK(name))'               | [name: "John"]                                || true
        'AND(NOT(ISNULL(name)), NOTBLANK(name))'               | [name: ""]                                    || false
        'AND(NOT(ISNULL(name)), NOTBLANK(name))'               | [name: null]                                  || false
        'OR(ISNULL(value), ISNUMBER(value))'                   | [value: "123"]                                || true
        'OR(ISNULL(value), ISNUMBER(value))'                   | [value: "abc"]                                || false
        'ISNULL(value)'                                         | [value: null]                                 || true
        'IF(ISNUMBER(input), VALUE(input), 0)'                 | [input: "123"]                                || new BigDecimal("123")
        'IF(ISNUMBER(input), VALUE(input), 0)'                 | [input: "abc"]                                || 0
        'NULL2DEFAULT(IF(ISBLANK(name), null, name), "Unknown")' | [name: "John"]                              || "John"
        'NULL2DEFAULT(IF(ISBLANK(name), null, name), "Unknown")' | [name: ""]                                  || "Unknown"
    }

    def "test logical operators with edge cases"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression                                              | bindings                                      || expectedResult
        // AND with multiple conditions
        'AND(true, true, true)'                                 | [:]                                           || true
        'AND(true, true, false)'                                | [:]                                           || false
        'AND(false, true, true)'                                | [:]                                           || false
        'AND(a > 0, b > 0, c > 0)'                              | [a: 1, b: 2, c: 3]                           || true
        'AND(a > 0, b > 0, c > 0)'                              | [a: 1, b: 2, c: -1]                          || false
        // OR with multiple conditions
        'OR(false, false, false)'                               | [:]                                           || false
        'OR(false, false, true)'                                | [:]                                           || true
        'OR(true, false, false)'                                | [:]                                           || true
        'OR(a > 10, b > 10, c > 10)'                            | [a: 1, b: 2, c: 15]                          || true
        'OR(a > 10, b > 10, c > 10)'                            | [a: 1, b: 2, c: 3]                           || false
        // NOT with complex expressions
        'NOT(AND(true, false))'                                 | [:]                                           || true
        'NOT(OR(false, false))'                                 | [:]                                           || true
        'NOT(a > b)'                                            | [a: 5, b: 10]                                || true
        'NOT(a > b)'                                            | [a: 15, b: 10]                               || false
        // Mixed logical operations
        'AND(OR(a > 0, b > 0), NOT(c < 0))'                     | [a: -1, b: 5, c: 10]                         || true
        'AND(OR(a > 0, b > 0), NOT(c < 0))'                     | [a: -1, b: -1, c: 10]                        || false
        'OR(AND(a > 0, b > 0), AND(c > 0, d > 0))'              | [a: 1, b: 2, c: -1, d: -1]                   || true
        'OR(AND(a > 0, b > 0), AND(c > 0, d > 0))'              | [a: -1, b: -1, c: -1, d: -1]                 || false
    }

    def "test IF function comprehensive cases"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression                                              | bindings                                      || expectedResult
        // Nested IF statements
        'IF(a > 10, "large", IF(a > 5, "medium", "small"))'     | [a: 15]                                       || "large"
        'IF(a > 10, "large", IF(a > 5, "medium", "small"))'     | [a: 8]                                        || "medium"
        'IF(a > 10, "large", IF(a > 5, "medium", "small"))'     | [a: 3]                                        || "small"
        // IF with different data types
        'IF(flag, 100, 0)'                                     | [flag: true]                                  || 100
        'IF(flag, 100, 0)'                                     | [flag: false]                                 || 0
        'IF(score >= 60, "Pass", "Fail")'                      | [score: 85]                                   || "Pass"
        'IF(score >= 60, "Pass", "Fail")'                      | [score: 45]                                   || "Fail"
        'IF(name != null, name, "Unknown")'                    | [name: "John"]                                || "John"
        'IF(name != null, name, "Unknown")'                    | [name: null]                                  || "Unknown"
        // IF with complex conditions
        'IF(AND(a > 0, b > 0), a + b, 0)'                       | [a: 5, b: 3]                                  || 8
        'IF(AND(a > 0, b > 0), a + b, 0)'                       | [a: -5, b: 3]                                 || 0
        'IF(OR(status == "ACTIVE", status == "PENDING"), 1, 0)' | [status: "ACTIVE"]                            || 1
        'IF(OR(status == "ACTIVE", status == "PENDING"), 1, 0)' | [status: "INACTIVE"]                          || 0
        // IF with arithmetic results
        'IF(x > y, x * 2, y * 2)'                              | [x: 10, y: 5]                                 || 20
        'IF(x > y, x * 2, y * 2)'                              | [x: 3, y: 8]                                  || 16
    }

    def "test null checking functions comprehensive"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression                                              | bindings                                      || expectedResult
        // ISNULL with different types
        'ISNULL(stringVal)'                                     | [stringVal: null]                             || true
        'ISNULL(stringVal)'                                     | [stringVal: ""]                               || false
        'ISNULL(stringVal)'                                     | [stringVal: "test"]                           || false
        'ISNULL(numberVal)'                                     | [numberVal: null]                             || true
        'ISNULL(numberVal)'                                     | [numberVal: 0]                                || false
        'ISNULL(numberVal)'                                     | [numberVal: 123]                              || false
        'ISNULL(boolVal)'                                       | [boolVal: null]                               || true
        'ISNULL(boolVal)'                                       | [boolVal: false]                              || false
        'ISNULL(boolVal)'                                       | [boolVal: true]                               || false
        // ISBLANK comprehensive tests
        'ISBLANK(text)'                                         | [text: null]                                  || true
        'ISBLANK(text)'                                         | [text: ""]                                    || true
        'ISBLANK(text)'                                         | [text: " "]                                   || false
        'ISBLANK(text)'                                         | [text: "  "]                                  || false
        'ISBLANK(text)'                                         | [text: "test"]                                || false
        'ISBLANK(text)'                                         | [text: "0"]                                   || false
        // NOTBLANK comprehensive tests
        'NOTBLANK(text)'                                        | [text: null]                                  || false
        'NOTBLANK(text)'                                        | [text: ""]                                    || false
        'NOTBLANK(text)'                                        | [text: " "]                                   || true
        'NOTBLANK(text)'                                        | [text: "test"]                                || true
        'NOTBLANK(text)'                                        | [text: "0"]                                   || true
        // ISNUMBER comprehensive tests
        'ISNUMBER(value)'                                       | [value: "123"]                                || true
        'ISNUMBER(value)'                                       | [value: "123.45"]                             || true
        'ISNUMBER(value)'                                       | [value: "-123"]                               || true
        'ISNUMBER(value)'                                       | [value: "-123.45"]                            || true
        'ISNUMBER(value)'                                       | [value: "0"]                                  || true
        'ISNUMBER(value)'                                       | [value: "0.0"]                                || true
        'ISNUMBER(value)'                                       | [value: "abc"]                                || false
        'ISNUMBER(value)'                                       | [value: "12.34.56"]                           || false
        'ISNUMBER(value)'                                       | [value: ""]                                   || false
        'ISNUMBER(value)'                                       | [value: null]                                 || false
    }

    def "test NULL2DEFAULT function edge cases"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression                                              | bindings                                      || expectedResult
        // Different default value types
        'NULL2DEFAULT(value, "default")'                       | [value: null]                                 || "default"
        'NULL2DEFAULT(value, "default")'                       | [value: "actual"]                             || "actual"
        'NULL2DEFAULT(value, 0)'                               | [value: null]                                 || 0
        'NULL2DEFAULT(value, 0)'                               | [value: 123]                                  || 123
        'NULL2DEFAULT(value, false)'                           | [value: null]                                 || false
        'NULL2DEFAULT(value, false)'                           | [value: true]                                 || true
        // Complex default values
        'NULL2DEFAULT(name, "Unknown User")'                   | [name: null]                                  || "Unknown User"
        'NULL2DEFAULT(name, "Unknown User")'                   | [name: "John Doe"]                            || "John Doe"
        'NULL2DEFAULT(score, -1)'                              | [score: null]                                 || -1
        'NULL2DEFAULT(score, -1)'                              | [score: 85]                                   || 85
        // Nested NULL2DEFAULT
        'NULL2DEFAULT(NULL2DEFAULT(primary, secondary), "fallback")' | [primary: null, secondary: "backup"]    || "backup"
        'NULL2DEFAULT(NULL2DEFAULT(primary, secondary), "fallback")' | [primary: null, secondary: null]        || "fallback"
        'NULL2DEFAULT(NULL2DEFAULT(primary, secondary), "fallback")' | [primary: "main", secondary: "backup"]  || "main"
    }

    def "test complex logical combinations"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression                                                              | bindings                                      || expectedResult
        // Complex business logic scenarios
        'IF(AND(NOT(ISNULL(user)), NOTBLANK(user)), "Valid User", "Invalid")'  | [user: "john"]                                || "Valid User"
        'IF(AND(NOT(ISNULL(user)), NOTBLANK(user)), "Valid User", "Invalid")'  | [user: null]                                  || "Invalid"
        'IF(AND(NOT(ISNULL(user)), NOTBLANK(user)), "Valid User", "Invalid")'  | [user: ""]                                    || "Invalid"
        // Score grading logic
        'IF(score >= 90, "A", IF(score >= 80, "B", IF(score >= 70, "C", IF(score >= 60, "D", "F"))))' | [score: 95] || "A"
        'IF(score >= 90, "A", IF(score >= 80, "B", IF(score >= 70, "C", IF(score >= 60, "D", "F"))))' | [score: 85] || "B"
        'IF(score >= 90, "A", IF(score >= 80, "B", IF(score >= 70, "C", IF(score >= 60, "D", "F"))))' | [score: 75] || "C"
        'IF(score >= 90, "A", IF(score >= 80, "B", IF(score >= 70, "C", IF(score >= 60, "D", "F"))))' | [score: 65] || "D"
        'IF(score >= 90, "A", IF(score >= 80, "B", IF(score >= 70, "C", IF(score >= 60, "D", "F"))))' | [score: 55] || "F"
        // User status validation
        'AND(NOTBLANK(username), ISNUMBER(age), OR(role == "admin", role == "user"))' | [username: "john", age: "25", role: "admin"] || true
        'AND(NOTBLANK(username), ISNUMBER(age), OR(role == "admin", role == "user"))' | [username: "", age: "25", role: "admin"] || false
        'AND(NOTBLANK(username), ISNUMBER(age), OR(role == "admin", role == "user"))' | [username: "john", age: "abc", role: "admin"] || false
        'AND(NOTBLANK(username), ISNUMBER(age), OR(role == "admin", role == "user"))' | [username: "john", age: "25", role: "guest"] || false
        // Data validation chains
        'IF(ISNULL(data), "Missing", IF(ISBLANK(data), "Empty", IF(ISNUMBER(data), "Number", "Text")))' | [data: null] || "Missing"
        'IF(ISNULL(data), "Missing", IF(ISBLANK(data), "Empty", IF(ISNUMBER(data), "Number", "Text")))' | [data: ""] || "Empty"
        'IF(ISNULL(data), "Missing", IF(ISBLANK(data), "Empty", IF(ISNUMBER(data), "Number", "Text")))' | [data: "123"] || "Number"
        'IF(ISNULL(data), "Missing", IF(ISBLANK(data), "Empty", IF(ISNUMBER(data), "Number", "Text")))' | [data: "abc"] || "Text"
    }

    def "test performance with complex logical expressions"() {
        when:
        def result = engine.evaluate(expression, bindings)
        then:
        result == expectedResult
        where:
        expression                                                              | bindings                                      || expectedResult
        // Multiple AND conditions
        'AND(a > 0, b > 0, c > 0, d > 0, e > 0)'                                | [a: 1, b: 2, c: 3, d: 4, e: 5]               || true
        'AND(a > 0, b > 0, c > 0, d > 0, e > 0)'                                | [a: 1, b: 2, c: 3, d: 4, e: -1]              || false
        // Multiple OR conditions
        'OR(a > 10, b > 10, c > 10, d > 10, e > 10)'                            | [a: 1, b: 2, c: 3, d: 4, e: 15]              || true
        'OR(a > 10, b > 10, c > 10, d > 10, e > 10)'                            | [a: 1, b: 2, c: 3, d: 4, e: 5]               || false
        // Deep nesting
        'IF(IF(IF(a > 0, true, false), IF(b > 0, true, false), false), "positive", "negative")' | [a: 5, b: 3] || "positive"
        'IF(IF(IF(a > 0, true, false), IF(b > 0, true, false), false), "positive", "negative")' | [a: -5, b: 3] || "negative"
        // Complex null checking chains
        'NULL2DEFAULT(NULL2DEFAULT(NULL2DEFAULT(a, b), c), "default")'          | [a: null, b: null, c: "value"]               || "value"
        'NULL2DEFAULT(NULL2DEFAULT(NULL2DEFAULT(a, b), c), "default")'          | [a: null, b: null, c: null]                  || "default"
    }
}
